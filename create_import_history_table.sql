-- 创建指标导入历史记录表
-- 执行此脚本来创建 yjzb_indicator_import_history 表

CREATE TABLE IF NOT EXISTS yjzb_indicator_import_history (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(12) DEFAULT '000000',
    import_type VARCHAR(50) NOT NULL DEFAULT 'excel',
    template_type VARCHAR(50),
    file_name VARCHAR(255),
    file_size BIGINT,
    period VARCHAR(20),
    total_count INT DEFAULT 0,
    success_count INT DEFAULT 0,
    fail_count INT DEFAULT 0,
    import_status VARCHAR(20) DEFAULT 'SUCCESS',
    import_result TEXT,
    error_message TEXT,
    import_duration BIGINT,
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6) DEFAULT NOW(),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);

-- 添加表注释
COMMENT ON TABLE yjzb_indicator_import_history IS '指标导入历史记录表 - 记录每次指标数据导入的详细信息';

-- 字段注释
COMMENT ON COLUMN yjzb_indicator_import_history.id IS '主键ID';
COMMENT ON COLUMN yjzb_indicator_import_history.tenant_id IS '租户ID';
COMMENT ON COLUMN yjzb_indicator_import_history.import_type IS '导入类型（excel-Excel导入、database-数据库导入、api-API导入）';
COMMENT ON COLUMN yjzb_indicator_import_history.template_type IS '模板类型（profit-利润表、balance-资产负债表、expense-三项费用、tax-锐利指标明细表）';
COMMENT ON COLUMN yjzb_indicator_import_history.file_name IS '导入文件名';
COMMENT ON COLUMN yjzb_indicator_import_history.file_size IS '文件大小（字节）';
COMMENT ON COLUMN yjzb_indicator_import_history.period IS '数据期间（格式：YYYY-MM）';
COMMENT ON COLUMN yjzb_indicator_import_history.total_count IS '总记录数';
COMMENT ON COLUMN yjzb_indicator_import_history.success_count IS '成功导入数';
COMMENT ON COLUMN yjzb_indicator_import_history.fail_count IS '失败记录数';
COMMENT ON COLUMN yjzb_indicator_import_history.import_status IS '导入状态（SUCCESS-成功、PARTIAL-部分成功、FAILED-失败）';
COMMENT ON COLUMN yjzb_indicator_import_history.import_result IS '导入结果详情（JSON格式，包含错误信息、未找到指标等）';
COMMENT ON COLUMN yjzb_indicator_import_history.error_message IS '错误信息';
COMMENT ON COLUMN yjzb_indicator_import_history.import_duration IS '导入耗时（毫秒）';
COMMENT ON COLUMN yjzb_indicator_import_history.create_user IS '创建人ID';
COMMENT ON COLUMN yjzb_indicator_import_history.create_dept IS '创建部门ID';
COMMENT ON COLUMN yjzb_indicator_import_history.create_time IS '创建时间';
COMMENT ON COLUMN yjzb_indicator_import_history.update_user IS '更新人ID';
COMMENT ON COLUMN yjzb_indicator_import_history.update_time IS '更新时间';
COMMENT ON COLUMN yjzb_indicator_import_history.status IS '数据状态（1-正常，0-禁用）';
COMMENT ON COLUMN yjzb_indicator_import_history.is_deleted IS '删除标记（0-未删除，1-已删除）';

-- 索引
CREATE INDEX IF NOT EXISTS idx_yjzb_indicator_import_history_import_type ON yjzb_indicator_import_history(import_type);
CREATE INDEX IF NOT EXISTS idx_yjzb_indicator_import_history_template_type ON yjzb_indicator_import_history(template_type);
CREATE INDEX IF NOT EXISTS idx_yjzb_indicator_import_history_period ON yjzb_indicator_import_history(period);
CREATE INDEX IF NOT EXISTS idx_yjzb_indicator_import_history_create_time ON yjzb_indicator_import_history(create_time DESC);
CREATE INDEX IF NOT EXISTS idx_yjzb_indicator_import_history_import_status ON yjzb_indicator_import_history(import_status);

-- 验证表创建成功
SELECT 'Table yjzb_indicator_import_history created successfully' as result;

-- 查看表结构
\d yjzb_indicator_import_history;
