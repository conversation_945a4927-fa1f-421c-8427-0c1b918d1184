<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.yjzb.mapper.IndicatorImportHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="indicatorImportHistoryResultMap" type="org.springblade.modules.yjzb.pojo.entity.IndicatorImportHistoryEntity">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="import_type" property="importType"/>
        <result column="template_type" property="templateType"/>
        <result column="file_name" property="fileName"/>
        <result column="file_size" property="fileSize"/>
        <result column="period" property="period"/>
        <result column="total_count" property="totalCount"/>
        <result column="success_count" property="successCount"/>
        <result column="fail_count" property="failCount"/>
        <result column="import_status" property="importStatus"/>
        <result column="import_result" property="importResult"/>
        <result column="error_message" property="errorMessage"/>
        <result column="import_duration" property="importDuration"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        id, tenant_id, import_type, template_type, file_name, file_size, period,
        total_count, success_count, fail_count, import_status, import_result,
        error_message, import_duration, status, is_deleted, create_user,
        create_dept, create_time, update_user, update_time
    </sql>

    <!-- 自定义分页查询 -->
    <select id="selectIndicatorImportHistoryPage" resultType="org.springblade.modules.yjzb.pojo.vo.IndicatorImportHistoryVO">
        SELECT 
            iih.id,
            iih.tenant_id,
            iih.import_type,
            iih.template_type,
            iih.file_name,
            iih.file_size,
            iih.period,
            iih.total_count,
            iih.success_count,
            iih.fail_count,
            iih.import_status,
            iih.import_result,
            iih.error_message,
            iih.import_duration,
            iih.status,
            iih.create_user,
            iih.create_dept,
            iih.create_time,
            iih.update_user,
            iih.update_time,
            cu.real_name AS createUserName,
            uu.real_name AS updateUserName
        FROM yjzb_indicator_import_history iih
        LEFT JOIN blade_user cu ON iih.create_user = cu.id
        LEFT JOIN blade_user uu ON iih.update_user = uu.id
        WHERE iih.is_deleted = 0
        <if test="indicatorImportHistory.importType != null and indicatorImportHistory.importType != ''">
            AND iih.import_type = #{indicatorImportHistory.importType}
        </if>
        <if test="indicatorImportHistory.templateType != null and indicatorImportHistory.templateType != ''">
            AND iih.template_type = #{indicatorImportHistory.templateType}
        </if>
        <if test="indicatorImportHistory.fileName != null and indicatorImportHistory.fileName != ''">
            AND iih.file_name LIKE CONCAT('%', #{indicatorImportHistory.fileName}, '%')
        </if>
        <if test="indicatorImportHistory.period != null and indicatorImportHistory.period != ''">
            AND iih.period = #{indicatorImportHistory.period}
        </if>
        <if test="indicatorImportHistory.importStatus != null and indicatorImportHistory.importStatus != ''">
            AND iih.import_status = #{indicatorImportHistory.importStatus}
        </if>
        ORDER BY iih.create_time DESC
    </select>

    <!-- 根据条件分页查询导入历史记录 -->
    <select id="selectIndicatorImportHistoryPageByCondition" resultType="org.springblade.modules.yjzb.pojo.vo.IndicatorImportHistoryVO">
        SELECT 
            iih.id,
            iih.tenant_id,
            iih.import_type,
            iih.template_type,
            iih.file_name,
            iih.file_size,
            iih.period,
            iih.total_count,
            iih.success_count,
            iih.fail_count,
            iih.import_status,
            iih.import_result,
            iih.error_message,
            iih.import_duration,
            iih.status,
            iih.create_user,
            iih.create_dept,
            iih.create_time,
            iih.update_user,
            iih.update_time,
            cu.real_name AS createUserName,
            uu.real_name AS updateUserName
        FROM yjzb_indicator_import_history iih
        LEFT JOIN blade_user cu ON iih.create_user = cu.id
        LEFT JOIN blade_user uu ON iih.update_user = uu.id
        ${ew.customSqlSegment}
    </select>

</mapper>
